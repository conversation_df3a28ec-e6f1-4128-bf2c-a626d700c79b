<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Gelişmiş 3D Görünürlük <PERSON>zi</title>
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.132/Build/Cesium/Cesium.js"></script>
    <link
      href="https://cesium.com/downloads/cesiumjs/releases/1.132/Build/Cesium/Widgets/widgets.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        overflow: hidden;
        background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
        color: #fff;
        height: 100vh;
      }

      #cesiumContainer {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
      }

      /* Kontrol Paneli Stilleri */
      .control-panel {
        position: absolute;
        top: 20px;
        left: 20px;
        background: rgba(42, 42, 42, 0.9);
        border-radius: 10px;
        padding: 20px;
        min-width: 300px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        z-index: 1000;
      }

      .control-panel h3 {
        margin-bottom: 15px;
        color: #fff;
        font-size: 16px;
        text-align: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding-bottom: 10px;
      }

      .control-group {
        margin-bottom: 15px;
      }

      .control-group label {
        display: block;
        margin-bottom: 5px;
        color: #fff;
        font-size: 14px;
        font-weight: 500;
      }

      .slider-container {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .slider {
        flex: 1;
        height: 6px;
        border-radius: 3px;
        background: #ddd;
        outline: none;
        opacity: 0.7;
        transition: opacity 0.2s;
        -webkit-appearance: none;
      }

      .slider:hover {
        opacity: 1;
      }

      .slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #4caf50;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .slider::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #4caf50;
        cursor: pointer;
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .value-display {
        background: rgba(255, 255, 255, 0.1);
        padding: 5px 10px;
        border-radius: 5px;
        min-width: 60px;
        text-align: center;
        font-family: monospace;
        font-size: 12px;
        color: #fff;
      }

      .reset-btn {
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.3s ease;
        margin-top: 10px;
        width: 100%;
      }

      .reset-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }
    </style>
  </head>
  <body>
    <div id="cesiumContainer"></div>

    <!-- Kontrol Paneli -->
    <div class="control-panel">
      <h3>🎛️ Tileset Kontrolleri</h3>

      <div class="control-group">
        <label for="screenSpaceErrorSlider">Screen Space Error:</label>
        <div class="slider-container">
          <input
            type="range"
            id="screenSpaceErrorSlider"
            class="slider"
            min="1"
            max="512"
            value="64"
            step="1"
          />
          <div class="value-display" id="screenSpaceErrorValue">64</div>
        </div>
      </div>

      <div class="control-group">
        <label for="baseScreenSpaceErrorSlider">Base Screen Space Error:</label>
        <div class="slider-container">
          <input
            type="range"
            id="baseScreenSpaceErrorSlider"
            class="slider"
            min="512"
            max="4096"
            value="1024"
            step="64"
          />
          <div class="value-display" id="baseScreenSpaceErrorValue">1024</div>
        </div>
      </div>

      <button class="reset-btn" onclick="resetToDefaults()">
        🔄 Varsayılan Değerlere Dön
      </button>
    </div>

    <script>
      // Ion token'ı
      Cesium.Ion.defaultAccessToken =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.H4k5QEiXbLCUvoPYg-6nf40QnKC3MjIerWvdKQxdves";

      // Değişkenler
      let viewer;
      let tileset;

      // Uygulamayı başlat
      async function initCesium() {
        try {
          // Cesium Viewer oluştur
          viewer = new Cesium.Viewer("cesiumContainer", {
            // terrainProvider: await Cesium.CesiumTerrainProvider.fromIonAssetId(
            //   1
            // ),

            animation: false,
            timeline: false,
            baseLayerPicker: false,
            geocoder: false,
            sceneModePicker: false,
            shadows: false,
            infoBox: false,
            selectionIndicator: false,
            skyBox: false,
            skyAtmosphere: false,
          });

          viewer.scene.globe.enableLighting = true;
          viewer.scene.globe.depthTestAgainstTerrain = true;

          try {
            tileset = await Cesium.Cesium3DTileset.fromUrl(
              "./data/3/tileset.json",
              {
                skipLevelOfDetail: true,
                baseScreenSpaceError: 1024,
                skipScreenSpaceErrorFactor: 16,
                skipLevels: 1,
                immediatelyLoadDesiredLevelOfDetail: false,
                loadSiblings: false,
                cullWithChildrenBounds: true,
                maximumScreenSpaceError: 64,
              }
            );

            viewer.scene.primitives.add(tileset);
            viewer.zoomTo(tileset);

            // Kontrolleri başlat
            initControls();
          } catch (error) {
            console.error("3D Tileset yüklenirken hata oluştu:", error);
          }
        } catch (error) {
          console.error("Cesium başlatılırken hata oluştu:", error);
          document.getElementById("status").textContent =
            "Hata: " + error.message;
        }
      }

      // Kontrol fonksiyonları
      function initControls() {
        const screenSpaceErrorSlider = document.getElementById(
          "screenSpaceErrorSlider"
        );
        const screenSpaceErrorValue = document.getElementById(
          "screenSpaceErrorValue"
        );
        const baseScreenSpaceErrorSlider = document.getElementById(
          "baseScreenSpaceErrorSlider"
        );
        const baseScreenSpaceErrorValue = document.getElementById(
          "baseScreenSpaceErrorValue"
        );

        // Screen Space Error kontrolü
        screenSpaceErrorSlider.addEventListener("input", function () {
          const value = parseInt(this.value);
          screenSpaceErrorValue.textContent = value;
          if (tileset) {
            tileset.maximumScreenSpaceError = value;
          }
        });

        // Base Screen Space Error kontrolü
        baseScreenSpaceErrorSlider.addEventListener("input", function () {
          const value = parseInt(this.value);
          baseScreenSpaceErrorValue.textContent = value;
          if (tileset) {
            tileset.baseScreenSpaceError = value;
          }
        });
      }

      // Varsayılan değerlere dön
      function resetToDefaults() {
        const screenSpaceErrorSlider = document.getElementById(
          "screenSpaceErrorSlider"
        );
        const screenSpaceErrorValue = document.getElementById(
          "screenSpaceErrorValue"
        );
        const baseScreenSpaceErrorSlider = document.getElementById(
          "baseScreenSpaceErrorSlider"
        );
        const baseScreenSpaceErrorValue = document.getElementById(
          "baseScreenSpaceErrorValue"
        );

        // Slider değerlerini sıfırla
        screenSpaceErrorSlider.value = 64;
        screenSpaceErrorValue.textContent = "64";
        baseScreenSpaceErrorSlider.value = 1024;
        baseScreenSpaceErrorValue.textContent = "1024";

        // Tileset değerlerini güncelle
        if (tileset) {
          tileset.maximumScreenSpaceError = 64;
          tileset.baseScreenSpaceError = 1024;
        }
      }

      // Uygulamayı başlat
      initCesium();
    </script>
  </body>
</html>
