<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Gelişmiş 3D Görünürlük Analizi</title>
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.132/Build/Cesium/Cesium.js"></script>
    <link
      href="https://cesium.com/downloads/cesiumjs/releases/1.132/Build/Cesium/Widgets/widgets.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        overflow: hidden;
        background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
        color: #fff;
        height: 100vh;
      }

      #cesiumContainer {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
      }
    </style>
  </head>
  <body>
    <div class="loading-screen" id="loadingScreen">
      <div class="spinner"></div>
      <div class="loading-text">Harita Yükleniyor...</div>
    </div>

    <div id="cesiumContainer"></div>

    <script>
      // Ion token'ı
      Cesium.Ion.defaultAccessToken =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************.H4k5QEiXbLCUvoPYg-6nf40QnKC3MjIerWvdKQxdves";

      // Değişkenler
      let viewer;

      // Uygulamayı başlat
      async function initCesium() {
        try {
          // Cesium Viewer oluştur
          viewer = new Cesium.Viewer("cesiumContainer", {
            terrainProvider: await Cesium.CesiumTerrainProvider.fromIonAssetId(
              1
            ),
            animation: false,
            timeline: false,
            baseLayerPicker: false,
            geocoder: false,
            sceneModePicker: false,
            shadows: true,
            infoBox: false,
            selectionIndicator: false,
            skyBox: false,
            skyAtmosphere: false,
          });

          viewer.scene.globe.enableLighting = true;
          viewer.scene.globe.depthTestAgainstTerrain = true;

          document.getElementById("loadingScreen").style.display = "none";

          const tms = new Cesium.UrlTemplateImageryProvider({
            url: "https://webtest03.netcad.com.tr/Netgis714Svn/map/MABIS/{x}/{y}/{z}?Session=132ea899e1d9449aa9a20799090d9c9d",
          });
          viewer.scene.imageryLayers.addImageryProvider(tms);
        } catch (error) {
          console.error("Cesium başlatılırken hata oluştu:", error);
          document.getElementById("status").textContent =
            "Hata: " + error.message;
        }
      }

      // Uygulamayı başlat
      initCesium();
    </script>
  </body>
</html>
